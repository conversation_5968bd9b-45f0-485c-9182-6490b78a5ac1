# 一体化AI业务平台需求V1.1

## 1. 项目愿景与业务目标 (Vision & Business Goals)

### 1.1 项目愿景(Elevator Pitch)

1. 构建能支撑公司各部门开展AI业务的中台系统

2. 在江苏一体化项目中销售AI中台，帮助客户基于一体机硬件开发部署AI应用

3. 以AI中台销售触点，销售满足政企客户需要的增值应用

### 1.2 核心业务驱动力 (Primary Business Drivers)

项目最主要的1-3个业务目标。

#### 1.2.1 构建AI业务的产品化底座

为各类AI业务赋能，构建产品化底座"卓语平台"，覆盖AI业务常用的"算数模台用"五大领域：

1. 算力：全面覆盖国产化+非国产化算力硬件的，实现对英伟达、昇腾、寒武纪等多样算力的集成和统一调配管理。

2. 数据：提供企业知识工程平台，管理企业领域知识的生产、治理、应用、迭代环节，为企业构建专业领域核心护城河。

3. 模型：提供大语言模型和深度学习模型的全链路生产管理平台，对国内外、开源和闭源、在线和自部署模型进行统一管理，并对外提供模型服务。

4. 平台：与企业现有业务平台对接，提供全栈大模型业务业务管理平台，覆盖业务域、管理域和开发域的平台服务。

5. 应用：提供大模型应用开发和服务开放平台，对应用开发和部署实现全生命周期管理，降低应用开发门槛，提升业务迭代效率；构建应用数据回流机制，利用生产数据反哺企业知识积累；通过应用市场，构建大模型应用销售能力，激发客户的大模型应用需求。

#### 1.2.2 增强大模型应用能力

利用卓语AI业务平台，在支撑业务和内部体校场景下，构建多种企业应用，并逐步把应用商品化，上架到应用集市进行销售。

#### 1.2.3 构建基于产品化底座的增值服务能力

通过卓语平台的应用集市服务，销售基于卓语平台的各类增值服务，为企业利用大模型实现提质增效。

***

## 2. 目标用户与核心用户旅程 (Target Users & Key Journeys)

本章节用于明确"我们为谁服务"，并以故事化的用户旅程来描述核心需求，而非罗列功能点。

### 2.1 目标用户画像 (User Personas)

1. 前提

   1. 企业有多个部门，多个部门之间，资源不共享；资源包括算力、数据集、模型、文件、知识库、数据库、应用。

   2. 通过项目空间进行隔离，项目空间之间平行，为简化问题，项目空间只有一层，不跟随企业的多层组织结构。

2. 企业管理员

   1. 管理项目空间

   2. 分配算力到项目空间

   3. 审核应用上架，购买应用，配置应用到项目空间，

   4. 管理平台配置

3. 项目管理员

   1. 管理项目空间内部角色设置

4. 开发角色

   1. 开发模型、数据、应用

   2. 配置模型、应用的业务总线

5. 企业员工

   1. 使用平台中的应用

6. API消费方

   1. 通过API方式访问业务总线

### 2.2 核心用户旅程 (Key User Journeys)

请基于上述用户画像，描述他们使用平台的典型场景。请遵循格式："作为一名<用户角色>，我想要<完成一个任务>，以便<实现一个价值>。"

#### 2.2.1 企业管理员

##### 2.2.1.1 管理项目空间

1. 用户故事: 作为一名企业管理员，我要管理项目空间，包括新增，编辑项目空间信息，设置项目管理员，以便让项目管理员可以管理团队中的角色

2. 涉及的关键环节:

   1. 新建项目空间：输入名称、描述 -> 选择项目管理员 -> 保存

   2. 编辑项目空间：查看项目空间列表 -> 进入项目空间详情 -> 编辑项目空间 -> 修改名称、描述、项目管理员、调整算力资源池 -> 保存

   3. 删除项目空间：查看项目空间列表 -> 进入项目空间详情 -> 确认 -> 删除成功

##### 2.2.1.2 管理算力

1. 用户故事: 作为一名企业管理员，我要希望为平台资源池添加算力资源，并分配给各个项目空间，以便让项目可以基于共享/独占资源池开展工作。

2. 涉及的关键环节:

   1. 新增算力：新增算力主机（本地/局域网） -> 新增显卡 -> 把显卡分配到资源池（共享/独占）

   2. 配置资源池：进入项目空间列表 -> 进入项目空间详情 -> 编辑项目空间 -> 选择该空间的独占算力资源池（或置空） -> 选择该空间的共享算力资源池（或置空） -> 保存

##### 2.2.1.3 管理平台配置

1. 用户故事: 作为一名企业管理员，我要希望定制平台名称，logo等信息，以便平台上所有用户能看到企业的标记。

2. 涉及的关键环节:

   1. 登陆 -> 进入平台配置 -> 修改平台名称、上传logo -> 保存

##### 2.2.1.4 购买应用

1. 用户故事: 作为一名企业管理员，我要了解哪些应用的员工需求占比，根据员工需求量来购买应用。

2. 涉及的关键环节:

   1. 登陆 -> 进入应用市场 -> 按员工需求占比来排序 -> 查看应用详情 -> 购买应用（这个版本无需支付，有这个环节）

##### 2.2.1.5 分配应用到指定项目空间

1. 用户故事: 作为一名企业管理员，我希望把企业应用（增值应用和自研应用）分配到项目空间，以便开发角色安装到项目中，提供给员工使用。

2. 涉及的关键环节:

   1. 进入企业管理员空间 -> 进入应用市场 -> 进入"企业应用"配置页面 -> 把该应用分配给对应的项目空间（上架到项目空间的应用模板）。

##### 2.2.1.6 下架企业应用

1. 用户故事: 作为一名企业管理员，我会视情况把企业应用（增值应用和自研应用）下架。

2. 涉及的关键环节:

   1. 进入企业管理员空间 -> 进入企业集市 -> 进入"企业应用"配置页面 -> 把该应用下架。

#### 2.2.2 企业员工

##### 2.2.2.1 浏览集市应用和登记"想要"的应用

1. 用户故事: 作为一名企业员工，我希望浏览应用市场，找到能解决工作痛点的应用，向企业管理员建议购买，以便在日常工作中使用这些应用。

2. 涉及的关键环节:

   * 登陆 -> 进入应用市场 -> 浏览应用 -> 点击"想要"按钮，提交需求，系统记录对该应用的想要需求。

##### 2.2.2.2 浏览和收藏项目应用

1. 用户故事: 作为一名企业员工，我希望浏览我所归属的项目空间上架的应用，以便添加到我的应用列表，在日常工作中使用这些应用。

2. 涉及的关键环节:

   * 登陆 -> 进入应用市场 -> 系统列出我项目空间名下所有应用 -> 添加收藏 -> 收藏后，应用出现在员工首页的我收藏的应用列表中（630版本先不做收藏）

##### 2.2.2.3 使用应用

1. 用户故事: 作为一名企业员工，我希望浏览我的应用列表，在日常工作中使用这些应用。

2. 涉及的关键环节:

   * 登陆 -> 进入我的应用 -> 系统列出所有我收藏的应用（这个版本先不做收藏，是列出我项目空间名下所有应用） -> 在新窗口打开应用 -> 使用应用

#### 2.2.3 项目管理员

##### 2.2.3.1 管理项目空间成员

1. 用户故事: 作为一名项目管理员，我要管理项目空间的人员和角色，一个用户可以具备多种角色，以便对应角色的用户有足够的权限开展工作。

2. 涉及的关键环节:

   * 登陆 -> 进入项目空间 -> 进入人员配置 -> 添加人员 -> 修改人员角色 -> 保存。

#### 2.2.4 开发工程师

开发工程师所有用户旅程都必须与项目空间绑定，登录后必须选择一个项目空间，才可以开展以下工作。

##### 2.2.4.1 数据治理

1. 用户故事: 作为一名企业开发工程师，我希望在平台上完成数据治理工作，生产出知识库和模型微调所需的数据集。数据集源头可以是来自于纸质文档，或者数字化文档，或者是网上的行业知识，甚至是应用和大模型产生的使用过程数据。

2. 涉及的关键环节:

   1. 数据集管理-导出：进入数据集列表 -> 选择数据集 -> 选择版本 -> 查看数据详情 -> 导出数据集

   2. 数据集管理-编辑：进入数据集列表 -> 选择数据集 -> 查看数据集详情 -> 编辑数据集详情 -> 保存

   3. 数据集管理-通过文件新建： 进入数据集列表 -> 选择新建数据集 -> 上传文件 -> 导入数据集，并设为版本1

   4. 数据集管理-通过数据源新建： 进入数据集列表 -> 选择新建数据集 -> 选择数据源 -> 执行导入SQL -> 导入数据集，并设为版本1

   5. 数据爬取：新建网站频道入口的数据集 -> 进入任务列表 -> 新建爬取任务 -> 选择数据集，选择版本 -> 选择爬取算子 -> 执行任务（爬取列表页 -> 爬取详情页 -> 清洗数据 -> 形成markdown） -> 导出为数据集新版本

   6. 文档转换：上传文件新建数据集 -> 进入任务列表 -> 新建文档转换任务 -> 选择数据集，选择版本 -> 选择转换算子 -> 执行转换任务（文档格式转换 -> 识别文档中的图片 -> 形成markdown） -> 导入为数据集

   7. 纸质文档导入：纸质文档拍照 -> 上传文件新建数据集 -> 进入任务列表 -> 新建纸质文档导入任务 -> 选择数据集，选择版本 -> 选择纸质文档导入算子 -> 执行转换任务（OCR识别 -> 形成markdown文档） -> 导入为数据集

   8. 数据回流：进入任务列表 -> 新建回流任务 -> 选择数据集，选择版本 -> 绑定应用、大模型的接口服务 -> 保存

   9. 数据清洗：进入任务列表 -> 新建清洗任务 -> 选择数据集，选择版本 -> 选择清洗算子 -> 执行清洗任务 -> 完成清洗任务 -> 导出为数据集新版本

   10. 数据增强：进入任务列表 -> 新建增强任务 -> 选择数据集，选择版本 -> 选择增强算子 -> 执行增强任务 -> 完成增强任务 -> 导出为数据集新版本

   11. 自动化数据标注：进入任务列表 -> 新建自动标注任务 -> 选择数据集，选择版本 -> 选择自动化标注算子 -> 执行标注任务 -> 完成标注任务 -> 导出为数据集新版本

   12. 人工数据标注：进入任务列表 -> 新建人工标注任务 -> 选择数据集，选择版本 -> 启动任务 -> 人工完成任务 -> 导出为数据集新版本

   13. 上架算子：开发算子 -> 进入算子列表 -> 新建算子 -> 选择算子类型（爬取、纸质文档导入、文档转换、清洗、增强、自动化标注 -> 输入算子基本信息，调用端点等信息 -> 保存

##### 2.2.4.2 模型微调

1. 用户故事: 作为一名企业开发工程师，我希望在平台上完成模型微调工作，完成后对模型进行部署，以便以API形式对外提供模型服务。

2. 涉及的关键环节:

   1. 模型微调：进入微调任务列表 -> 新建微调任务 -> 选择基座模型 -> 选择模型 -> 选择算力资源池 -> 启动微调任务 -> 查看微调任务进度 -> 完成微调任务 -> 导出模型到模型仓库

##### 2.2.4.3 模型部署

1. 用户故事: 作为一名企业开发工程师，我希望在平台上部署自训练的模型或者从互联网下载的模型，以便以API形式对外提供模型服务。

2. 涉及的关键环节:

   1. 上传模型：进入模型仓库 -> 选择上传模型 -> 完成模型上传

   2. 部署模型：进入模型仓库 -> 选中需要部署的模型（来自于上传，或来自于训练结果） -> 新建部署任务 -> 选择算力资源池 -> 完成部署

##### 2.2.4.4 知识库管理

1. 用户故事: 作为一名企业开发工程师，我希望在平台上开发知识库，以便在后续应用中使用知识库。

2. 涉及的关键环节:

   1. 基于数据集新建知识库：进入知识库列表 -> 选择新建知识库 -> 知识库来源选择数据集 -> 设置知识库其他配置 -> 完成创建知识库

   2. 基于文件新建知识库：进入知识库列表 -> 选择新建知识库 -> 上传文件 -> 设置知识库其他配置 -> 完成创建知识库

##### 2.2.4.5 数据库管理

1. 用户故事: 作为一名企业开发工程师，我希望新增和管理各类关系型和非关系型数据库的链接，以便在应用开发和数据治理过程中使用这些数据库。

2. 涉及的关键环节:

   1. 新建和管理数据源：进入数据源列表 -> 新建数据源 -> 设置数据源类型、名称、地址、用户名和密码 -> 保存。

##### 2.2.4.6 应用开发

1. 用户故事: 作为一名企业开发工程师，我希望在平台上开发应用，以便以API形式对外提供应用服务。

2. 涉及的关键环节:

   1. 新建应用：进入应用列表 -> 新建应用 -> 通过工作流、Agent或者对话形式开发应用 -> 选择知识库、数据库等知识来源 -> 保存应用版本 -> 发布应用->应用出现在员工的项目应用列表中，允许员工使用。应用管理：进入应用列表 -> 选择应用 -> 查看应用详情 -> 查看应用访问日志、修改应用配置。

   2. 应用评估：进入任务列表 -> 新建应用评估任务 -> 选择回流数据集，选择版本 -> 系统完成回流数据集新老版本的切割（老版本数据用于评估任务，新版本数据继续执行回流任务） -> 执行评估任务 -> 完成评估任务 -> 导出为数据集新版本 -> 系统分析数据集形成评估报告

   3. 应用上升：进入应用列表 -> 选择应用 -> 查看应用详情 -> 选择版本 -> 申请应用上升。

   4. 应用上升撤销：进入上升申请列表 -> 选择应用 -> （如果是上升申请未通过）撤销申请应用上升。

##### 2.2.4.7 应用部署

1. 用户故事: 作为一名企业开发工程师，我希望在平台上部署系统管理员分配的应用，以便项目空间内的员工能够使用该应用。一个模板能发布为一个或多个应用，每个应用可以具备独有的配置。

2. 涉及的关键环节:

   1. 上架企业知识库应用：进入应用模板列表 -> 找到企业知识库应用模板 -> 安装应用 -> 配置对接哪个知识库 -> 发布应用。

   2. 上架AI+BI应用：进入应用模板列表 -> 找到AI+BI应用模板 -> 安装应用 -> 配置数据库链接、BI算子 -> 发布应用。

   3. 上架文档审计应用：进入应用模板列表 -> 找到文档审计应用模板 -> 安装应用 -> 配置文档取数模板 -> 发布应用。

##### 2.2.4.8 应用管理

1. 用户故事: 作为一名企业开发工程师，我希望在平台上管理项目应用上下架，以便控制项目空间下的应用可以被员工使用。

2. 涉及的关键环节:

   1. 应用上架：进入应用列表 -> 选择应用 -> 查看应用详情 -> （如果是下架状态）上架该应用。

   2. 应用下架：进入应用列表 -> 选择应用 -> 查看应用详情 -> （如果是上架状态）下架该应用。

##### 2.2.4.9 服务治理

1. 用户故事: 作为一名企业开发工程师，我要为指定前端应用或者外部应用申请API Key，以便前端或者外部应用能够访问模型和应用API接口。

2. 涉及的关键环节:

   1. 新建外部资源端点：进入外部资源列表 -> 新建资源端点 -> 设置名称 -> 选择模型服务，或者应用服务 -> 选择对外暴露的已部署的模型或者应用 -> 保存端点。

   2. 申请API Key：进入外部资源列表 -> 选择一个资源端点 -> 查看详情 -> 为当前项目空间申请生成API key -> 完成申请

##### 2.2.4.10 任务管理

1. 用户故事: 作为一名企业开发工程师，我希望在平台上管理数据治理、模型部署任务（启动、停止），以保证这些任务正常执行。

2. 涉及的关键环节:

   1. 查看进度：进入任务列表 -> 查看任务详情 -> 查看任务进度、名称、描述等基本信息

   2. 调整任务：进入任务列表 -> 查看任务详情 -> 启动、停止任务

#### 2.2.5 API消费方

##### 2.2.5.1 调用API

1. 用户故事: 作为前端应用，或者第三方应用，我希望提供API Key，经过平台鉴权后，访问平台开放出来的模型API或应用API，以便完成业务流程。

2. 涉及的关键环节:

   * 发起对平台API接口的访问 -> 提供API Key，完成鉴权 -> 访问能力服务。

***

## 3. 核心功能与边界 (Core Feature & Boundaries)

本章节定义平台的"能力内核"与"不做之事"，为架构和产品设计提供约束。

### 3.1 平台核心功能支柱 (Core Functional Pillars)

卓语平台实现大语言模型和大模型应用的开发、部署、服务业务，具备算力管理、数据治理、模型管理、应用开发，及相关业务管理模块。

平台面向企业员工、开发人员、管理员提供服务域、开发域、管理域功能、算力、数据、模型、应用、平台管理层，面向外部应用提供API服务和治理能力，覆盖模型、应用、平台管理层。平台可根据不同规模客户的业务需求，按需裁剪，以实现最简单的算力管理，模型能力开放，到全栈的大模型应用开发部署形志。

#### 3.1.1 算力

1. 算力支持：集成和统一调配管理国产化与非国产化算力硬件，涵盖英伟达、昇腾、寒武纪等多样算力。

2. 算力组合：企业管理员可新增算力主机及主机上显卡，包括一机多卡，多机多卡；支持把算力分配到共享或独占资源池。

3. 算力分配：企业管理员可把资源池重新分配给项目使用，包括共享和独占两种方式。共享资源池中的显卡资源在多个项目空间之间，以抢先占用的方式共享；独占资源池中的显卡资源分配给指定项目空间后，即使进入空闲状态，其他项目空间也不可使用。一个项目组可以挂接零个或多个资源池。

#### 3.1.2 数据

1. 知识工程：提供企业知识工程平台，管理企业领域知识的生产、治理和迭代。

2. 数据集管理：开发工程师可进行数据集的导出、编辑、新建等操作，支持与本地文件、知识库、数据库等多个数据源的导入和导出。

3. 任务框架：平台具备完整的任务管理框架，支持单个和批量任务执行，支持任务断点续执行，可自动化和手动执行多种任务。

4. 任务算子：各类任务以算子形式引入任务框架，任务算子包括数据爬取、识别、清洗、标注、增强、数据回流等多种数据治理任务。数据标注支持自动化标注和人工标注。文档识别支持word、pdf、ppt等多种格式，支持OCR和视觉大模型等多种识别方式。

#### 3.1.3 模型

1. 模型库：提供大语言模型和深度学习模型的全链路生产管理平台，统一管理国内外、开源和闭源、在线和自部署模型，支持上传模型到模型库。

2. 模型部署：开发工程师可使用项目空间的资源池，从模型库中选择模型部署

3. 模型总线：通过服务开放平台，开发工程师可为前端或外部应用申请API Key，实现对模型的API接口访问。

4. 模型微调：开发工程师可进行模型微调、部署等工作，包括项目空间范围内选择基座模型、算力资源池，选择数据集等。

#### 3.1.4 平台

1. 对接现有平台：提供全栈大模型业务管理平台，覆盖业务域、管理域和开发域的平台服务，并与企业现有业务平台对接，例如单点登陆。

2. 平台总线：通过服务开放平台，开发工程师可为前端或外部应用申请API Key，实现对平台能力的API接口访问。

3. 企业管理：企业管理员可针对AI业务进行项目空间管理、平台配置、应用购买和分配、算力资源登记和分配等操作。

4. 项目管理：项目管理员可管理项目空间内部角色设置。

#### 3.1.5 应用

1. 应用开发：提供大模型应用开发，以及必需服务的开发配置功能，如知识库、数据库、工具等。

2. 应用总线：通过服务开放平台，开发工程师可为前端或外部应用申请API Key，实现对应用的API接口访问。

3. 应用管理：提供应用市场构建大模型应用销售能力以及企业内应用流转能力，对应用开发和部署实现全生命周期管理，包括新建、管理、启用、停用、上架、下架应用等。

### 3.2 非功能性要求 (Non-Functional Requirements)

#### 3.2.1 性能要求 (Performance Requirements)

* **响应时间**: <2秒/<5秒/<10秒

* **并发用户**: 100/500/1000/2000+

* **系统可用性**: 95%/99%/99.9%

#### 3.2.2 安全要求 (Security Requirements)

* **数据加密级别**: 基础/中等/高等

* **审计日志**: 基础记录/详细记录/实时监控

* **访问控制**: 基于角色

#### 3.2.3 易用性要求 (Usability Requirements)

* **学习成本**: <1天/<1周/需培训

* **界面复杂度**: 简洁/标准/功能丰富

### 3.3 外部系统依赖与关系 (External System Dependencies)

确平台需要与哪些现有系统进行交互，以及交互的方式和目的。

#### 3.3.1 账号系统

* **对接目的:** 本系统定位为业务系统，账号体系由企业客户提供，如果企业不提供账号体系，则本系统负责维护用户账号。

* **数据流向:**

  * **平台 - 外部系统**:单点登陆

  * **外部系统 - 平台**: 无

* **交互方式:**

  * **API调用**：实现单点登陆和登陆状态维护

* **依赖关键性:**

  * **强依赖:** 如果客户提供账号体系，若此系统中断，平台核心功能不可用。

  * **弱依赖:** 如果客户不提供账号体系，则自建账号体系，一次若此系统中断，平台功能不受影响。

### 3.4 范围边界 (What's Out of Scope)

确宣布"不做"什么，和"做"什么同样重要，这有助于管理预期和防止范围蔓延。

* 不涉及硬件设备的生产与制造，仅聚焦于算力硬件的集成和管理功能。

* 不提供基础的操作系统开发与维护，仅在操作系统上实现平台功能部署。

* 不负责企业内部非AI相关业务系统的核心功能开发，仅提供对接集成服务。

* 初期不支持多租户跨企业级的权限管理体系，仅满足单一企业内部分级管理。

* 不涉及数据隐私合规的法律审核服务，仅提供数据治理技术工具。

* 不负责行业大模型的预训练过程，仅支持基于基座模型的微调与管理。

***

## 4. 核心架构概念设计 (High-Level Architectural Concepts)

本章节定义平台的数据和系统交互基础，指导架构设计。

### 4.1 业务实体总览（按业务域分组）

1. 组织与用户管理 (Organization & User Management)

   1. 企业 (Enterprise)

   2. 项目空间 (Project Space)

   3. 用户 (User)

   4. 角色 (Role)

2. 算力管理 (Computing Power Management)

   1. 算力主机 (Computing Host)

   2. 显卡 (GPU)

   3. 资源池 (Resource Pool)

3. 数据治理 (Data Management)

   1. 数据集 (Dataset)

   2. 数据集版本 (Dataset Version)

   3. 数据 (DataSet Record)

4. 模型管理 (Model Management)

   1. 模型仓库 (Model Repository)

   2. 模型 (Model)

   3. 模型版本 (Model Version)

   4. 模型部署 (Model Deployment)

5. 应用管理 (Application Management)

   1. 项目应用 (Project Application)

   2. 项目应用版本 (Application Version)

   3. 知识库 (Knowledge Base)

   4. 数据源 (Data Source)

   5. 工具（Tools)

   6. 应用评估(Application Eveluation)

6. 任务管理 (Task Management)

   1. 任务 (Task)

   2. 算子 (Operator)

7. 服务管理 (Service Management)

   1. 资源端点 (Resource Endpoint)

   2. API密钥 (API Key)

8. 应用市场 (Application Market)

   1. 企业应用（即增值应用，应用模板）

   2. 项目应用

   3. 员工应用"想要"需求

   4. 应用购买记录

   5. 应用上升申请

9. 运营管理

   1. 平台配置 (Platform Configuration)

   2. 算力注册

   3. 应用购买记录

### 4.2 详细实体定义

描述各个业务实体，不包括实体之间的关系

#### 4.2.1 组织与用户管理

##### 4.2.1.1 企业 (Enterprise)

* **描述**: 平台的使用主体，代表一个独立的公司或组织。

* **关键属性**:

  * `enterprise_id` (PK)

  * `name`: 企业名称

  * `logo_url`: 企业Logo

* **主要业务规则**:

  * 平台配置（如名称、Logo）是企业级别的。

##### ******* 项目空间 (Project Space)

* **描述**: 企业内部用于隔离资源、数据、模型和应用的逻辑单元。

* **关键属性**:

  * `team_id` (PK)

  * `enterprise_id` (FK)

  * `name`: 空间名称

  * `description`: 描述

  * 独占资源池列表

  * 共享资源池列表

  * 应用列表

  * 用户、角色列表

* **主要业务规则**:

  * 资源（算力资源池、数据、模型、应用、任务）在项目空间间隔离。

  * 每个项目空间有一名或多名项目管理员，零或多名开发人员，零或多名企业用户。

  * 企业管理员可以创建、编辑、删除项目空间，设置项目管理员。

##### *******. 用户 (User)

* **描述**: 平台的所有操作者，包括管理员、开发者和普通员工。

* **关键属性**:

  * `user_id` (PK)

  * `username`: 用户名

  * `password_hash`: 密码（如果不由外部系统管理）

* **主要业务规则**:

  * 一个用户可归属于多个项目空间。

  * 一个用户在多个项目空间，拥有多个角色。

##### *******. 角色 (Role)

* **描述**: 定义用户在平台中的权限集合。

* **关键属性**:

  * `role_id` (PK)

  * `role_name`: 角色名称 (如：企业管理员, 项目管理员, 开发角色, 企业员工)

* **主要业务规则**:

  * 权限基于角色进行控制（RBAC）。

***

#### 4.2.2 算力管理

##### ******* 算力主机 (Compute Host)

* **描述**: 提供算力资源的物理或虚拟机。

* **关键属性**:

  * `host_id` (PK)

  * `hostname`: 主机名/IP地址

  * `status`: 状态（在线/离线）

##### *******  显卡 (GPU)

* **描述**: 算力的核心单元。

* **关键属性**:

  * `gpu_id` (PK)

  * `host_id` (FK)

  * 品牌：

  * `name`: 显卡型号

  * `memory`: 显存大小

* **主要业务规则**:

  * 一个算力主机可以拥有多个显卡。

##### ******* 资源池 (Resource Pool)

* **描述**: 显卡资源的逻辑集合，分为共享和独占两种类型。

* **关键属性**:

  * `pool_id` (PK)

  * `name`: 资源池名称

  * `type`: 类型 (共享 / 独占)

* **主要业务规则**:

  * 企业管理员将显卡分配到资源池。

  * 企业管理员将资源池分配给项目空间。

  * 一个项目空间可以关联零个或多个资源池。

***

#### 4.2.3 数据治理

##### ******* 数据集 (Dataset)

* **描述**: 用于知识库构建和模型微调的结构化或非结构化数据集合。

* **关键属性**:

  * `dataset_id` (PK)

  * `team_id` (FK): 所属项目空间

  * `name`: 数据集名称

  * `description`: 描述

  * `source_type`: 来源类型（文件上传, 数据库, 爬取, 回流）

  * version\_needed：是否实现版本管理

* **主要业务规则**:

  * 数据集归属于项目空间。

  * 数据集有版本管理。

##### ******* 数据集版本 (Dataset Version)

* **描述**: 数据集在特定时间点的快照。

* **关键属性**:

  * `version_id` (PK)

  * `dataset_id` (FK)

  * `version_tag`: 版本号 (e.g., v1.0, v2.1)

  * `created_at`: 创建时间

  * `storage_path`: 存储路径

* **主要业务规则**:

  * 对数据集的修改（如清洗、增强）会产生新的版本。

##### *******. 数据 (DataSet Record)

* **描述**: 数据集指定版本下的具体数据。

* **关键属性**:

  * `record_id` (PK)

  * `dataset_id` (FK)

  * `version_id` (FK)

  * `content`: 具体的数据内容，以json格式存储

* **主要业务规则**:

  * 用于从业务系统导入数据创建数据集。

#### 4.2.4 模型管理 (Model Management)

##### ******* 模型仓库 (Model Repository)

* **描述**: 存储和管理模型文件的中央仓库，是模型资产的来源。
* **关键属性**:
  * `repo_id` (PK)
  * `name`: 仓库名称 (如：社区开源模型, 企业自研模型)
  * `description`: 描述
* **主要业务规则**:
  * 一个项目空间对应一个模型仓库。

##### ******* 模型 (Model)

* **描述**: 一个具体的、可用于微调或部署的AI模型。
* **关键属性**:
  * `model_id` (PK)
  * `repo_id` (FK): 所属仓库
  * `team_id` (FK): 所属项目空间（仅对自研模型）
  * `name`: 模型名称 (e.g., Llama3-8B-Instruct)
  * `description`: 描述
  * `family`: 模型系列 (e.g., Llama3, GLM)
* **主要业务规则**:
  * 模型可以从模型仓库导入，也可以是项目空间微调的产物。
  * 一个模型可以有多个版本。

##### ******* 模型版本 (Model Version)

* **描述**: 模型在特定时间点的快照，包含具体的模型文件和配置。
* **关键属性**:
  * `version_id` (PK)
  * `model_id` (FK)
  * `version_tag`: 版本号 (e.g., v1.0)
  * `storage_path`: 模型文件存储路径
  * `created_at`: 创建时间
* **主要业务规则**:
  * 模型微调会产生新的模型版本。

##### ******* 模型部署 (Model Deployment)

* **描述**: 一个正在运行并提供API服务的模型实例，底层是通过任务来实现。
* **关键属性**:
  * `deployment_id` (PK)
  * `team_id` (FK): 部署所在的项目空间
  * `task_id` (FK): 对应的任务ID
  * `model_version_id` (FK): 部署的模型版本
  * `name`: 部署实例名称
  * `endpoint_url`: 服务访问地址
  * `resource_pool_id` (FK): 使用的算力资源池
  * `replicas`: 副本数量
  * `status`: 状态 (部署中, 运行中, 已停止, 失败)
* **主要业务规则**:
  * 将一个模型版本部署到指定的算力资源池中。
  * 部署后，可以通过`endpoint_url`提供服务。

#### 4.2.5 应用管理

##### ******* 项目应用 (Project Application)

* **描述**: 应用模板在项目空间中的一个具体安装实例，面向最终用户提供服务。

* **关键属性**:

  * `app_id` (PK)

  * `team_id` (FK): 所属项目空间

  * `template_id` (FK): 安装自哪个模板

  * `name`: 应用实例名称

  * `description`: 描述

  * `config`: 根据模板`config_schema`填写的具体配置

  * `status`: 状态 (上架/下架)

  * tag：平台应用标签（例如客户服务、内容创作、市场营销）

  * icons：图标

  * `development_type`: 开发方式 (工作流 / Agent / 对话)

  * create\_time：创建时间

* **主要业务规则**:

  * 项目应用归属于项目空间。

  * 开发者在项目空间内，基于被授权的`应用模板`安装和配置后，生成一个`项目应用`。

  * 一个`应用模板`可以被安装成多个`项目应用`实例。

##### ******* 项目应用版本 (Application Version)

* **描述**: 项目应用在特定时间点的快照，是应用发布、上升、回滚的基本单位。 **注意：此实体主要用于从零开始开发（非基于模板安装）的场景。**

* **关键属性**:

  * `version_id` (PK)

  * `app_id` (FK)

  * `version_tag`: 版本号 (e.g., v1.0, v2.1)

  * `description`: 版本说明/更新日志

  * `status`: 状态 (开发中, 已发布, 归档)

  * `created_at`: 创建时间

* **主要业务规则**:

  * 当开发者发布一个从零开发的应用时，会创建一个新的应用版本。
  * 只有"已发布"状态的版本才能被用户使用。

##### ******* 知识库 (Knowledge Base)

* **描述**: 为大模型应用提供专业领域知识的存储库。

* **关键属性**:

  * `kb_id` (PK)

  * `team_id` (FK): 所属项目空间

  * `name`: 知识库名称

  * icon：图标

  * description：描述

  * `source_type`: 来源类型 (数据集 / 文件)

* **主要业务规则**:

  * 基于数据集或文件创建。

  * 由应用在运行时调用。

##### ******* 数据源 (Data Source)

* **描述**: 外部数据库的连接信息。

* **关键属性**:

  * `source_id` (PK)

  * `team_id` (FK): 所属项目空间

  * `name`: 数据源名称

  * `type`: 数据库类型 (MySQL, PostgreSQL, etc.)

  * `connection_string`: 连接字符串

* **主要业务规则**:

  * 用于从业务系统导入数据创建数据集。

##### ******* 工具(Tool)

* **描述**: 应用运行过程中调用的可复用能力。它可以是项目内实现的一个工作流，也可以是对一个企业级`资源端点`的引用和配置。

* **关键属性**:

  * `tool_id` (PK)

  * `team_id` (FK): 所属项目空间

  * `name`: 工具名称

  * `type`: 工具类型 (内部工作流 / 外部API)

  * `resource_endpoint_id`: (FK, nullable) 如果是外部API类型，关联的资源端点

  * `parameter`: 工具运行的参数（如对外部API的特定配置）

* **主要业务规则**:

  * 项目工具与项目应用类似，是企业工具的一个实例，使用项目级配置运行。

##### 4.2.5.6 应用评估(Application Evaluation)

* **描述**: 对应用运行结果进行评估，利用数据回流工具，收集应用的输出内容形成数据集，利用任务框架，调用应用评估算子，计算应用评估结果，输出应用评估报告。

* **关键属性**:

  * `evaluation_id` (PK)

  * `team_id` (FK): 所属项目空间

  * `name`: 评估任务名称

  * `task_id`: (FK)

  * `app_id` (FK)

* **主要业务规则**:

  * 项目工具与项目应用类似，是企业工具的一个实例，使用项目级配置运行。

##### 4.2.5.7 应用生命周期图示

```mermaid
graph TD
    subgraph "项目空间 A (自研应用)"
        Dev1("开始开发<br/>(创建项目应用)") --> Dev2("发布 v1.0<br/>(创建应用版本)");
        Dev2 --> Dev3{{"申请上升<br/>(提交应用上升申请)"}};
    end

    subgraph "企业管理空间"
        Dev3 --> Admin1{审核};
        Admin1 -- "通过" --> Admin2["创建企业应用(内部)<br/>& 创建应用模板 v1.0"];
        Admin1 -- "驳回" --> Dev2;

        Market1["应用市场<br/>(增值应用)"] -- "购买" --> Admin3["创建企业应用(增值)<br/>& 创建应用模板"];

        Admin2 -- "分配" --> Admin4{{"分配模板<br/>(更新模板 allowed_team_ids)"}};
        Admin3 -- "分配" --> Admin4;
    end

    subgraph "项目空间 B (使用应用)"
        Admin4 -- "授权给空间 B" --> DevB1["应用模板可用"];
        DevB1 -- "安装与配置" --> DevB2("创建新的项目应用实例<br/>(上架后可使用)");
    end

    classDef dev fill:#e6f7ff,stroke:#91d5ff,stroke-width:2px;
    classDef admin fill:#f6ffed,stroke:#b7eb8f,stroke-width:2px;
    classDef use fill:#fff7e6,stroke:#ffd591,stroke-width:2px;

    class Dev1,Dev2,Dev3 dev;
    class Admin1,Admin2,Admin3,Admin4,Market1 admin;
    class DevB1,DevB2 use;
```

***

#### 4.2.6 任务管理 (Task Management)

##### 4.2.6.1 任务 (Task)

* **描述**: 一个可执行的后台作业，如数据处理、模型微调、模型部署等。

* **关键属性**:

  * `task_id` (PK)

  * `team_id` (FK): 所属项目空间

  * `name`: 任务名称

  * `type`: 任务类型 (数据爬取, 文档转换, 清洗, 增强, 标注, 模型微调, 模型部署, 应用评估)

  * `status`: 状态 (待执行, 执行中, 已完成, 失败)

  * `created_by` (FK, user\_id)

* **主要业务规则**:

  * 任务与项目空间和用户关联，一个项目可以有多个任务，一个任务对应一个创建者。

  * 任务使用算子来执行具体操作，一个任务可以执行多个算子，多个算子之间是串行执行关系。每个算子所使用的参数以

  * 任务使用一个数据集的版本作为输入，并以一个数据集的新版本作为输出

##### 4.2.6.2 算子 (Operator)

* **描述**: 在任务框架中执行具体原子操作的可复用组件。

* **关键属性**:

  * `operator_id` (PK)

  * `name`: 算子名称

  * `type`: 算子类型 (爬取, 清洗, OCR, 增强, 标注等)

  * `endpoint_url`: 调用端点

* **主要业务规则**:

  * 开发者可以开发并上架新的算子。

#### 4.2.7 服务管理

##### ******* 资源端点 (Resource Endpoint)

* **描述**: 由企业统一引入和管理的外部服务资源（如外部大模型API、第三方工具服务等），或者是内部部署的大模型、应用对外提供API服务，都需先注册为服务资源。资源端点是全企业范围的，作为后续分配给项目空间使用的基础。

* **关键属性**:

  * `resource_endpoint_id` (PK)

  * `name`: 端点名称

  * `description`: 描述信息

  * `service_type`: 服务类型 (外部模型 / 外部应用 / 外部工具)

  * `endpoint_url`: 外部服务的URL

  * `enterprise_api_key`: 企业用于调用该服务的统一密钥（如有）

* **主要业务规则**:

  * 由企业管理员注册和管理。
  * 一个外部服务对应一个资源端点。

##### ******* API密钥 (API Key)

* **描述**: 项目空间访问某个"资源端点"的凭证。

* **关键属性**:

  * `api_key_id` (PK)
  * `team_id` (FK): 所属项目空间
  * `resource_endpoint_id` (FK): 关联的资源端点
  * `api_key`: 生成的密钥字符串
  * `status`: 状态 (有效 / 失效)
  * `created_at`: 申请时间
  * `created_by` (FK, user_id): 申请人

* **主要业务规则**:

  * 开发人员在项目空间内，为指定的资源端点申请API密钥。
  * 一个项目空间可以为同一个资源端点申请多个密钥，也可以为不同资源端点申请密钥。

#### 4.2.8 应用市场

##### ******* 企业应用 (Enterprise Application)

* **描述**: 由企业购买的增值应用或由内部项目应用"上升"而来的，可在全企业范围内分配的应用。
* **关键属性**:
  * `enterprise_app_id` (PK)
  * `name`: 应用名称
  * `description`: 应用描述
  * `app_type`: 类型 (增值 / 内部自研)
  * `status`: 状态 (未购买 / 已购买 / 已上升)
* **主要业务规则**:
  * 企业管理员审核项目应用的"上升"申请，通过后创建一条"内部自研"类型的企业应用记录。
  * 企业管理员"购买"市场上的增值应用。
  * 每个"企业应用"对应一个或多个"应用模板"。

  * app\_id：唯一标识

  * app\_name：应用名称

  * app\_type：应用类型（增值/内部）

  * `tag`：平台应用标签（例如客户服务、内容创作、市场营销）

  * status：状态（未购/已购/待上升/已上升）

  * publish\_status：上架/下架

  * developer\_id：开发人员id

  * allowed\_scope：分配的项目空间列表

  * app\_description：应用描述

  * template\_id：模板ID

  * template\_version：模板版本

  * create\_time：创建时间

* **主要业务规则**:
  * 由企业管理员将模板分配给一个或多个项目空间。

  * 企业管理员购买增值应用

  * 开发人员开发和上架项目应用 ➝ 开发人员提交项目应用上升申请 ➝ 通过后变为企业应用 ➝ 企业管理员可以下架（➝ 企业管理员可以下架上架）

##### ******* 员工"想要"需求

* **描述**: 由企业员工对增值应用提交的应用需求清单。

* **关键属性**:

  * want\_id：需求ID

  * userid：提出人

  * app\_id：想要应用唯一标识

  * create\_time：创建时间

* **主要业务规则**:

  * 一个企业员工对一个增值应用提交一个"想要"应用需求，系统记录信息

  * 企业管理员通过"想要"需求数量评估是否购买增值应用。

##### ******* 应用购买记录

* **描述**: 企业管理员从平台购买增值应用的交易记录。

* **关键属性**:

  * `order_id` (PK)
  * `enterprise_app_id` (FK): 购买的应用
  * `buyer_id` (FK, user_id): 企业管理员
  * `enterprise_id` (FK): 所属企业
  * `amount`: 金额
  * `status`: 状态 (已下单／已支付／已完成)
  * `created_at`: 交易时间

* **主要业务规则**:

  * 企业管理员对增值应用进行购买，购买时记录下交易记录。

##### ******* 应用上升申请 (Promotion Application)

* **描述**: 开发人员对项目应用发起的上升申请记录。

* **关键属性**:

  * `application_id` (PK)
  * `proposer_id` (FK, user_id): 提交人
  * `team_id` (FK): 来自于哪个空间
  * `app_version_id` (FK): 申请上升的项目应用版本
  * `status`：审批中 / 通过 / 驳回
  * `comment`：审批意见
  * `created_at`: 申请时间
  * `reviewed_at`: 审批时间

* **主要业务规则**:

  * 开发人员对一个"已发布"的`项目应用版本`发起上升申请。
  * 企业管理员进行审核，如果审核通过，则会创建一个新的`企业应用`和`应用模板`。

### 4.3 实体关系矩阵

| **源实体**      | **目标实体**        | **关系类型** | **基数**  | **描述**                                               |
| --------------- | ------------------- | ------------ | --------- | ------------------------------------------------------ |
| **组织 & 用户** |                     |              |           |                                                        |
| 企业            | 项目空间            | 一对多       | 1..*      | 一个企业包含多个项目空间                               |
| 项目空间        | 用户                | 多对多       | *..*      | 用户和空间是多对多，通过角色关联                       |
| 用户            | 角色                | 多对多       | *..*      | 用户和角色是多对多                                     |
| **算力**        |                     |              |           |                                                        |
| 算力主机        | 显卡                | 一对多       | 1..*      | 一台主机可有多张显卡                                   |
| 资源池          | 显卡                | 一对多       | 1..*      | 一个资源池包含多张显卡                                 |
| 项目空间        | 资源池              | 多对多       | *..*      | 项目空间可使用多个资源池（通过共享/独占）              |
| **数据 & 模型** |                     |              |           |                                                        |
| 项目空间        | 数据集              | 一对多       | 1..*      | 项目空间拥有多个数据集                                 |
| 用户            | 数据集              | 一对多       | 1..*      | 用户创建数据集                                         |
| 数据集          | 数据集版本          | 一对多       | 1..*      | 一个数据集有多个版本                                   |
| 任务            | 数据集版本          | 多对多       | *..*      | 任务消耗/产出数据集版本                                |
| 模型仓库        | 模型                | 一对多       | 1..*      | 仓库包含多个模型                                       |
| 项目空间        | 模型                | 一对多       | 1..*      | 项目空间微调产出私有模型                               |
| 模型            | 模型版本            | 一对多       | 1..*      | 一个模型有多个版本                                     |
| 模型版本        | 模型部署            | 一对多       | 1..*      | 一个模型版本可以被多次部署                             |
| 项目空间        | 模型部署            | 一对多       | 1..*      | 项目空间拥有多个模型部署实例                           |
| 资源池          | 模型部署            | 一对多       | 1..*      | 模型部署消耗资源池算力                                 |
| **应用**        |                     |              |           |                                                        |
| 企业应用        | 应用模板            | 一对多       | 1..*      | 一个企业应用有多个模板版本                             |
| 应用模板        | 项目应用            | 一对多       | 1..*      | 一个模板可安装为多个项目应用实例                       |
| 项目空间        | 项目应用            | 一对多       | 1..*      | 项目空间拥有多个应用实例                               |
| 项目应用        | 项目应用版本        | 一对多       | 1..*      | （自研）项目应用有多个版本                             |
| 项目应用        | 知识库              | 多对多       | *..*      | 应用运行时调用知识库                                   |
| 项目应用        | 数据源              | 多对多       | *..*      | 应用运行时连接数据源                                   |
| 项目应用        | 工具                | 多对多       | *..*      | 应用运行时使用工具                                     |
| 数据集          | 知识库              | 一对一       | 1..1      | 知识库由一个数据集转换而来                             |
| **服务 & 任务** |                     |              |           |                                                        |
| 资源端点        | API密钥             | 一对多       | 1..*      | 一个外部资源可为多个项目空间生成密钥                   |
| 项目空间        | API密钥             | 一对多       | 1..*      | 项目空间可申请多个不同资源的密钥                       |
| 用户            | API密钥             | 一对多       | 1..*      | 用户申请API密钥                                        |
| 项目空间        | 任务                | 一对多       | 1..*      | 项目空间内有多个任务                                   |
| 用户            | 任务                | 一对多       | 1..*      | 一个用户可创建多个任务                                 |
| 任务            | 算子                | 一对多       | 1..*      | 一个任务包含多个按顺序执行的算子步骤                   |
| **应用市场**    |                     |              |           |                                                        |
| 用户            | 员工"想要"需求     | 一对多       | 1..*      | 用户提交"想要"需求                                     |
| 企业应用        | 员工"想要"需求     | 一对多       | 1..*      | 一个应用可被多个用户"想要"                             |
| 用户            | 应用购买记录        | 一对多       | 1..*      | 管理员购买应用                                         |
| 企业应用        | 应用购买记录        | 一对多       | 1..*      | 一个应用可被多次购买（如按年订阅）                     |
| 用户            | 应用上升申请        | 一对多       | 1..*      | 开发者提交上升申请                                     |
| 项目应用版本    | 应用上升申请        | 一对一       | 1..1      | 对一个特定应用版本发起申请                             |

### 4.4 核心业务约束和规则

1. **资源隔离**: 所有核心资源（算力、数据、模型、应用）都与`项目空间`绑定，实现团队间的严格隔离。

2. **算力分配**: 算力资源（显卡）必须先加入`资源池`，然后由企业管理员将`资源池`分配给`项目空间`。

3. **角色权限**: 用户权限由其`角色`决定。`企业管理员`拥有最高权限，管理企业级配置、团队和算力。`项目管理员`管理团队内的人员和角色。`开发角色`进行数据、模型、应用的开发。`企业员工`使用平台应用。

4. **数据版本化**: 对`数据集`的任何处理（清洗、增强、标注）都会生成一个新的`数据集版本`，保证数据的可追溯性。

5. **应用生命周期**:

   * **开发**: `开发角色`在项目空间内创建`应用`。

   * **上架申请**: `开发角色`可为自研`应用`申请上架到企业应用市场。

   * **审批**: `企业管理员`审核上架申请。

   * **分配**: `企业管理员`将集市中的`应用模板`（包括购买的增值应用和上架的自研应用）分配给指定的`项目空间`。

   * **安装**: `开发角色`在项目空间内根据`应用模板`安装和配置，生成可用的`项目应用`实例。

   * **使用**: `企业员工`使用项目空间内的`项目应用`。

6. **服务暴露**: 内部的`模型部署`或外部的`资源端点`，均通过为`项目空间`生成专属`API密钥`的方式来提供服务。

## 5. 核心业务流程 (Core Business Processes)

本章节将离散的用户旅程和实体定义串联起来，从业务目标的视角，描述平台如何实现其核心价值主张。我们将流程归纳为三大核心价值链。

### 5.1 从数据到价值：模型与知识的生产流程

流程的核心目标是将原始、零散的数据转化为可被业务直接调用、蕴含洞察的AI能力（模型服务或知识库）。这是平台实现知识沉淀和算法自研的根本。

1. **数据汇集与准备 (Data Aggregation & Preparation)**:
    * **起点**: `开发角色`面临一个AI任务，其原始资料可能是业务数据库、本地文件（如PDF、Word）、纸质文档或网络资源。
    * **过程**:
        * 开发者在`项目空间`内创建`数据集`，通过文件上传、数据库连接或爬虫任务等方式，将原始资料统一纳管。
        * 针对非结构化的数据，开发者可创建`任务(Task)`，编排一系列`算子(Operator)`（如文档转换、OCR识别、数据清洗、数据增强等）对`数据集`进行处理。
    * **产物**: 经过处理后，生成一个结构化、干净的、带有版本号的`数据集版本`。

2. **知识内化 (Knowledge Internalization)**:
    * **分支一：构建知识库 (RAG)**
        * `开发角色`新建`知识库`，并选择一个`数据集版本`作为知识源。
        * 平台在后台对数据进行切片、向量化（Embedding）并索引，形成可供检索的知识库。
        * **产物**: 一个可被`项目应用`关联调用的`知识库`。
    * **分支二：训练新模型 (Fine-tuning)**
        * `开发角色`创建模型微调`任务`。
        * **输入**: 选择一个`基座模型版本`、一个用于训练的`数据集版本`，并从`资源池`中指定算力。
        * 平台调度算力执行微调任务。
        * **产物**: 一个新的、经过微调的`模型(Model)`及其`模型版本`，保存在模型仓库中。

3. **能力服务化 (Capability as a Service)**:
    * `开发角色`选择一个`模型版本`（无论是自研的还是导入的），为其创建`模型部署`任务。
    * 任务成功后，平台生成一个`模型部署`实例，并提供一个内部访问的 `endpoint_url`。
    * 此`endpoint_url`可被项目空间内的应用或工具直接消费。
    * 若需对外或跨项目提供服务，`企业管理员`可将其注册为`资源端点`，由`API密钥`进行访问控制。
    * **产物**: 一个稳定运行、可被调用的AI模型服务。

### 5.2 从创意到赋能：应用的开发与消费流程

流程的核心目标是快速响应业务需求，将AI能力封装为用户可感的应用产品，并交付到最终用户手中，形成业务闭环。

1. **应用来源 (Application Sourcing)**:
    * **路径一：内部自研 (In-house Development)**
        * `开发角色`在`项目空间`中，从零开始创建`项目应用`。
        * 在开发界面中，开发者可以编排工作流、配置Agent，并关联项目内的`知识库`、`数据源`和`模型部署`服务。
        * 开发完成后，发布`项目应用版本`并将其**上架**，使其对项目空间内的`企业员工`可见。
    * **路径二：外部采购与引入 (Marketplace Acquisition)**
        * `企业员工`在`应用市场`中浏览`增值应用`，并通过 **[我想要]** 功能表达需求。
        * `企业管理员`根据员工需求和业务价值，**购买**应用。购买后，该应用成为一个`企业应用`。
        * 管理员将该`企业应用`（及其`应用模板`）**分配**给一个或多个`项目空间`。
        * `开发角色`在自己的项目空间内，看到可安装的应用模板，执行**安装**，生成一个新的`项目应用`实例，并进行必要的配置后上架。

2. **应用上升通道 (Application Promotion Path)**:
    * 对于一个成熟的、有推广价值的自研`项目应用`，`开发角色`可以为其某个版本提交`应用上升申请`。
    * `企业管理员`在管理后台收到申请并进行**审批**。
    * 批准后，平台会自动基于该项目应用，创建一个全局的`企业应用`和`应用模板`，使其可以被分配给其他项目空间，实现组织内的复用和推广。

3. **最终消费 (Final Consumption)**:
    * `企业员工`登录平台，在其个人**员工工作台**上，能看到所有其所在项目空间已上架的`项目应用`。
    * 员工点击应用卡片，即可在新的窗口中使用应用，解决实际业务问题。

### 5.3 平台生命线：治理与管理流程

流程是保障平台稳定、安全、高效运行的基础，由`企业管理员`和`项目管理员`主导，确保资源得到合理分配，权限得到有效管控。

1. **顶层设置 (Top-Level Setup)**:
    * `企业管理员`首次配置平台时，会设置企业级的`平台名称`和`Logo`。
    * 管理员会**登记**企业拥有的所有`算力节点`（物理机/虚拟机），并将GPU等资源组织成不同的`资源池`（如"训练池"、"推理池"）。

2. **项目空间初始化与维护 (Project Space Initialization & Maintenance)**:
    * `企业管理员`根据业务线或部门需求，**创建**`项目空间`。
    * 创建或编辑项目空间时，管理员会为其**分配**一个或多个`资源池`，定义其算力配额。
    * `项目管理员`在该项目空间内，**添加/移除成员**，并为成员**分配角色**（如开发角色、企业员工），控制其操作权限。

3. **全局服务治理 (Global Service Governance)**:
    * 对于需要被多个项目空间共享的外部服务（如企业自有的CRM API、第三方数据服务等），`企业管理员`会将其注册为`资源端点`。
    * `开发角色`在自己的项目空间中，可以浏览到这些可用的`资源端点`，并为其**申请**`API密钥`，从而在应用开发中安全地调用这些外部服务。

4. **应用市场生态管理 (App Market Ecosystem Management)**:
    * `企业管理员`是应用市场的“守门人”。
    * **决策**是否购买新的`增值应用`。
    * **审批**来自项目空间的`应用上升申请`，决定哪些内部应用可以成为企业级标准应用。
    * **管理**所有`企业应用`的生命周期，如分配、更新或下架。

## 6. 页面功能与UI设计详述

本章节是产品需求文档(PRD)的核心，它将前面定义的信息架构(IA)具象化为可被UI/UX设计师和前端工程师理解的具体页面设计。每个页面都需要清晰地描述其目的、导航关系、核心UI元素(UIIE - User Interface Information Elements)以及背后的数据实体与交互逻辑。

### 6.1 整体信息架构脑图

```mermaid
mindmap
  root((一体化AI业务平台))
    A[员工视图]
      A1(员工工作台)
      A2(应用市场)
      A3(应用详情)
    B[开发者视图]
      B1(项目空间选择页)
      B2(项目概览)
      B3(应用管理)
        B3.1{{项目应用列表}}
        B3.2{{可安装企业应用}}
        B3.3{{应用开发与配置}}
      B4(数据管理)
        B4.1{{数据集管理}}
        B4.2{{知识库管理}}
        B4.3{{数据源管理}}
      B5(模型中心)
        B5.1{{模型管理}}
        B5.2{{模型部署管理}}
      B6(平台工具)
        B6.1{{任务管理}}
        B6.2{{算子管理}}
        B6.3{{服务管理}}
      B7(项目设置)
        B7.1{{成员与角色管理}}
    C[企业管理视图]
      C1(企业概览)
      C2(组织与项目空间管理)
      C3(应用市场管理)
      C4(算力管理)
      C5(服务治理)
      C6(平台设置)
```

### 6.2 A. 员工视图 (Employee View)

视图服务于组织内的普通员工(`企业员工`角色)，是AI能力的最终消费者。设计目标是**简洁、直观、易用**，让他们能毫不费力地发现和使用AI应用来提升工作效率。

#### **A1. 员工工作台 (Employee Dashboard)**

* **页面名称**: 员工工作台
* **目的**: 为员工提供一个统一的工作入口，快速访问已被授权使用的项目应用。
* **导航关系**:
  * **来源**: 用户登录后，如果角色是"企业员工"，则直接进入此页面。也可以通过顶部导航栏的"工作台"链接随时返回。
  * **去向**: 点击应用卡片，在新标签页中打开具体的应用。点击顶部导航栏可进入"应用市场"。
* **核心UI元素 (UIIE)**:
  * **顶部导航栏**:
    * 左侧为平台Logo和名称。
    * 中间为导航项：**[工作台]** (当前页), **[应用市场]**。
    * 右侧为用户头像和下拉菜单（包含"个人信息"、"退出登录"）。
  * **主内容区**:
    * 一个标题，如 "我的应用"。
    * **应用卡片网格 (Card Grid)**: 页面主体，用卡片形式平铺展示所有该员工有权访问的应用。
* **数据实体与交互**:
  * **主要实体**: `项目应用 (Project Application)`
  * **页面加载时**:
    * 系统获取当前登录的`用户(User)`信息。
    * 根据用户所属的`项目空间(Project Space)`列表，查询所有空间下状态为"已上架"的`项目应用`。
    * 将查询到的`项目应用`列表渲染为应用卡片网格。
  * **应用卡片 (Application Card) UI**:
    * **数据展示**: 每张卡片需显示以下`项目应用`的属性：
      * `icons` (应用图标)
      * `name` (应用名称)
      * `description` (应用简短描述)
      * `tag` (应用标签，如"客户服务")
    * **交互**:
      * 整个卡片可点击。
      * **点击效果**: 点击后，在新浏览器标签页中打开该应用的实际使用界面（URL从`项目应用`的配置中获取）。

#### **A2. 应用市场 (Application Market)**

* **页面名称**: 应用市场
* **目的**: 让员工能集中浏览企业提供或购买的所有应用，并可以对自己感兴趣但尚未购买的应用表达"想要"的意愿，为企业管理员的购买决策提供数据支持。
* **导航关系**:
  * **来源**: 从"员工工作台"(A1) 的顶部导航栏点击 [应用市场] 进入。
  * **去向**: 点击应用卡片进入"应用详情页"(A3)。
* **核心UI元素 (UIIE)**:
  * **顶部导航栏**: 同A1。
  * **侧边筛选/分类栏**:
    * **搜索框**: 按应用名称 (`app_name`) 进行模糊搜索。
    * **分类筛选**: 按 `app_type` ("增值应用", "内部应用") 进行筛选。
    * **标签筛选**: 按 `tag` (如"客户服务"、"内容创作") 进行多选筛选。
  * **主内容区**:
    * 一个标题，如"应用市场"。
    * **应用卡片网格**: 与A1类似，但展示的是`企业应用`。
* **数据实体与交互**:
  * **主要实体**: `企业应用 (Enterprise Application)`, `员工"想要"需求 (Want)`
  * **页面加载时**:
    * 获取并展示所有`publish_status`为"已上架"的`企业应用`。
    * 同时，系统需要获取当前用户已经"想要"过的应用列表，用于在卡片上展示不同的按钮状态。
  * **应用卡片 (Marketplace Card) UI**:
    * **数据展示**:
      * `app_description` (应用描述), `app_name`, `tag`等。
      * 一个显著的角标或标签，标明 `app_type` ("增值应用"或"内部自研")。
      * **"想要"人数**: 展示该应用被"想要"的总数，从`员工"想要"需求`表中统计。
    * **交互**:
      * **点击卡片主体**: 导航到该应用的"应用详情页"(A3)。
      * **"想要"按钮**:
        * **初始状态**: 如果用户**未曾**对该应用提交过"想要"需求，按钮显示为 **[我想要]**，可点击。
        * **点击 [我想要]**:
          * 向后端发送请求，在 `员工"想要"需求` 表中为当前用户和该应用新增一条记录。
          * 成功后，按钮状态变为"已想要"，并变为灰色不可点击状态。同时，卡片上显示的"想要"人数+1。
        * **置灰状态**: 如果用户**已经**提交过"想要"需求，按钮直接显示为 **[已想要]**，且为灰色不可点击。

#### **A3. 应用详情 (Application Details)**

* **页面名称**: 应用详情
* **目的**: 提供一个应用的详细信息，帮助员工全面了解其功能、用途和价值。
* **导航关系**:
  * **来源**: 从"应用市场"(A2) 页面点击任一应用卡片进入。
  * **去向**: 可通过顶部导航栏返回"工作台"或"应用市场"。
* **核心UI元素 (UIIE)**:
  * **顶部导航栏**: 同A1。
  * **主内容区**:
    * **应用头信息区**:
      * 左侧：`icons` (应用大图标), `app_name` (应用名称)。
      * 右侧：**[我想要]** / **[已想要]** 按钮，功能与A2中的完全相同。
      * 下方：`tag` 列表。
    * **详细描述区**:
      * 可以分为几个Tab或长页面区域。
      * **"功能介绍"**: 展示详细的 `app_description`，可以使用富文本格式，包含截图、列表等。
      * **"版本历史"**: （可选）展示 `template_version` 的更新历史。
      * **"评价"**: （未来扩展）展示用户评价。
* **数据实体与交互**:
  * **主要实体**: `企业应用 (Enterprise Application)`
  * **页面加载时**:
    * 从URL中获取 `app_id`。
    * 根据 `app_id` 查询对应的`企业应用`实体，并将其详细信息渲染到页面的各个部分。
    * 同样需要查询当前用户对该应用的"想要"状态，以正确显示按钮。
  * **交互**:
    * 核心交互是 **[我想要]** 按钮，其逻辑与 A2 页面中的按钮完全一致。

### 6.3 B. 开发者/项目管理视图 (Developer/Project Admin View)

视图是平台的核心生产区域，服务于`开发角色`和`项目管理员`。设计目标是**专业、高效、灵活**，提供从数据处理、模型训练到应用开发的全链路工具链，同时确保项目内的资源和成员得到良好管理。

#### **B1. 项目空间选择页 (Project Space Selector)**

* **页面名称**: 选择您的项目空间
* **目的**: 作为开发者视图的入口，强制用户选择一个明确的工作上下文（项目空间），避免后续操作发生混淆。
* **导航关系**: 用户登录后，若角色包含"开发角色"或"项目管理员"，则首先进入此页面。点击项目空间卡片后，进入该空间的"项目概览"(B2)页面。
* **核心UI元素**: 全屏居中布局，一个清晰的标题（"请选择一个项目空间以继续"），以及展示用户所属所有`项目空间`的卡片网格。
* **数据实体与交互**:
  * **实体**: `项目空间`
  * **交互**: 点击项目空间卡片，将`team_id`存入会话并导航至该项目空间的概览页 `/space/{team_id}/dashboard`。

#### **B2. 项目概览 (Project Dashboard)**

* **页面名称**: 项目概览
* **目的**: 为开发者提供项目空间的整体快照，展示关键信息和资源使用情况，并提供常用操作的快捷入口。
* **导航关系**:
  * **来源**: 从"项目空间选择页"(B1)进入。是进入具体项目后的主页。
  * **去向**: 可通过左侧导航栏进入此项目空间下的任何其他功能页面。
* **核心UI元素**:
  * **左侧导航栏**:
    * 顶部显示当前项目空间名称，并提供切换功能。
    * 导航菜单包含：**[概览]**(当前), **[应用]**, **[数据]**, **[模型]**, **[平台工具]**, **[项目设置]**。
  * **右侧主内容区**:
    * **资源使用统计卡片**: 展示应用数、数据集数、模型部署数、算力配额等。
    * **最近活动**: 显示项目空间内最近事件的时间线。
    * **快捷操作**: 一组按钮，如 **[+ 新建应用]**, **[+ 上传数据]**, **[部署模型]**。
* **数据实体与交互**:
  * **实体**: `项目空间`, `项目应用`, `数据集`, `模型部署` 等聚合信息。
  * **交互**: 点击左侧导航切换页面，点击快捷操作按钮导航到对应功能。

#### **B3. 应用 (Application)**

是项目空间内进行应用开发、安装和管理的中心。

##### **B3.1. 项目应用列表 (Project Apps List)**

* **页面名称**: 应用管理
* **目的**: 集中展示和管理当前项目空间内所有的`项目应用`，包括自研的应用和从企业模板安装的应用。
* **导航关系**:
  * **来源**: 点击左侧导航栏的 **[应用]**。
  * **去向**:
    * 点击"新建应用"或"安装应用"进入`B3.2`或`B3.3`。
    * 点击列表中的应用名称或"配置"链接，进入该应用的配置详情页 (`B3.3`)。
    * 点击"申请上升"按钮，打开"应用上升申请"对话框。
* **核心UI元素 (UIIE)**:
  * **页面顶栏**:
    * **[+ 新建应用]** 按钮：用于从零开始创建新应用。
    * **[安装应用]** 按钮：导航至 `B3.2 可安装企业应用` 页面。
  * **主内容区**:
    * **应用列表表格 (Table)**:
      * **列**:
        * `name`: 应用名称
        * `status`: 状态 (上架/下架/开发中)
        * `development_type`: 开发方式 (自研/模板安装)
        * `version_tag`: 当前版本号
        * `created_at`: 创建时间
        * `actions`: 操作列 (配置, 上/下架, 申请上升, 删除)
* **数据实体与交互**:
  * **主要实体**: `项目应用 (Project Application)`, `项目应用版本 (Application Version)`
  * **页面加载时**: 拉取当前`team_id`下的所有`项目应用`及其最新`项目应用版本`信息，填充表格。
  * **交互**:
    * **上/下架**: 点击后，调用API更改`项目应用`的`status`，成功后刷新该行数据。
    * **申请上升**:
      * 仅当应用为"自研"且状态为"上架"时，此按钮可用。
      * 点击后，弹出对话框，让用户选择一个`项目应用版本`并发起`应用上升申请`。
      * 表单包含：`app_version_id` (下拉选择版本), `comment` (申请理由文本域)。
      * 提交后，在`应用上升申请`表中创建一条记录，按钮变为"审核中"的不可点状态。
    * **删除**: 点击后，弹出二次确认对话框，确认后删除`项目应用`。仅"开发中"或"已下架"的应用可被删除。

##### **B3.2. 可安装企业应用 (Installable Enterprise Apps)**

* **页面名称**: 从市场安装应用
* **目的**: 允许开发者浏览所有已被企业管理员授权给当前项目空间的企业应用，并选择一个进行安装。
* **导航关系**:
  * **来源**: 从"项目应用列表"(`B3.1`)页面点击 **[安装应用]** 按钮。
  * **去向**: 点击"安装"后，进入该应用的"应用开发与配置"页面(`B3.3`)，以完成最终配置。
* **核心UI元素 (UIIE)**:
  * **页面布局**: 与员工视图的"应用市场"(`A2`)类似，使用卡片网格布局。
  * **应用卡片网格**: 展示所有`allowed_scope`包含当前`team_id`的`企业应用`。
* **数据实体与交互**:
  * **主要实体**: `企业应用 (Enterprise Application)`
  * **页面加载时**: 拉取所有被授权给当前项目空间的`企业应用`列表。
  * **应用卡片 (Installable App Card) UI**:
    * **数据展示**: `app_name`, `app_description`, `tag`等。
    * **交互**:
      * 卡片上有一个醒目的 **[安装]** 按钮。
      * **点击 [安装]**:
        * 系统以后端`企业应用`的模板信息为基础，在当前项目空间内创建一个新的`项目应用`记录，其初始状态为"开发中"。
        * 导航到这个新创建应用的配置页面(`B3.3`)，URL中包含新的`app_id`。

##### **B3.3. 应用开发与配置 (App Development & Configuration)**

* **页面名称**: 应用开发 / 应用配置
* **目的**: 为开发者提供一个统一的界面，用于从零开发新应用，或对一个已安装的应用进行详细配置。
* **导航关系**:
  * **来源**:
    * 在`B3.1`页面点击 **[+ 新建应用]**。
    * 在`B3.1`页面点击一个已有应用的"配置"链接。
    * 在`B3.2`页面点击一个企业应用的 **[安装]** 按钮。
  * **去向**: 保存配置后，可返回`B3.1`列表页。
* **核心UI元素 (UIIE)**:
  * **页面布局**: Tab页或多段式表单布局。
  * **Tab/表单区域**:
    * **1. 基本信息**:
      * 表单项：`name` (应用名称), `icons` (图标上传), `description` (描述), `tag`。
    * **2. 开发与构建 (Development/Build)**: (仅对"自研"应用)
      * 此区域为应用的核心开发界面，具体形态根据`development_type`决定。可能是类似工作流编排的画布、Agent配置的表单，或是对话式流程设计的界面。
    * **3. 运行配置 (Runtime Config)**: (对所有应用类型)
      * **模板参数**: 如果应用是基于模板安装的，这里会渲染出`config_schema`定义的配置项，如API密钥、数据库连接字符串等，让开发者填写。
      * **知识库关联**: 提供一个下拉或搜索框，让开发者从本项目的`知识库`中选择一个或多个进行关联。
      * **数据源关联**: 类似地，关联`数据源`。
      * **工具使用**: 关联本项目可用的`工具(Tool)`。
    * **4. 版本管理 (Versioning)**:
      * 显示该`项目应用`的所有`项目应用版本`列表。
      * 提供 **[发布新版]** 按钮，点击后可为当前配置创建一个新的、不可变的`项目应用版本`。
* **数据实体与交互**:
  * **主要实体**: `项目应用`, `项目应用版本`, `知识库`, `数据源`
  * **交互**:
    * **保存**: 页面底部有"保存"按钮，点击后将所有配置更新到`项目应用`实体。
    * **发布新版**: 点击后，弹出对话框要求输入`version_tag`和`description`(更新日志)，确认后创建一个新的`项目应用版本`记录。

#### **B4. 数据 (Data)**

模块聚合了与数据相关的多种管理功能，是构建高质量模型和RAG应用的基础。

##### **B4.1. 数据集管理 (Dataset Management)**

* **页面名称**: 数据集管理
* **目的**: 管理项目空间内所有用于模型训练或知识库构建的数据集。
* **导航关系**: 点击左侧导航栏的 **[数据]** -> **[数据集]**。
* **核心UI元素 (UIIE)**:
  * **页面顶栏**:
    * **[+ 新建数据集]** 按钮。
  * **主内容区**:
    * **数据集列表表格**:
      * **列**: `name`, `description`, `source_type`, `version_needed` (是否版本化), 最新版本号, 创建时间, 操作。
      * **交互**: 点击数据集名称可进入详情页，查看所有版本和数据记录。
* **数据实体与交互**:
  * **实体**: `数据集`, `数据集版本`, `任务`
  * **新建数据集流程**:
    * 点击 **[+ 新建数据集]**，弹出对话框。
    * **表单**: 输入`name`, `description`。选择`source_type` (文件上传/数据库/...)。
    * 根据选择的来源，进入下一步（如文件上传框）。
    * 成功后，创建`数据集`实体和第一个`数据集版本`。
  * **发起数据任务**:
    * 在数据集列表或详情页，有 **[执行数据任务]** 按钮。
    * 点击后，弹出对话框，让用户创建一个新的`任务(Task)`。
    * **表单**:
      * `name`: 任务名称。
      * `type`: 任务类型 (下拉选择：数据爬取/文档转换/清洗/增强/标注...)。
      * `input_version_id`: 选择一个输入的数据集版本。
      * `operator_id`: 根据任务类型，选择一个或多个`算子`。
      * `output_version_tag`: 为任务成功后产生的新版本命名。
    * 提交后，创建一个`任务`记录，并可在`B6.1 任务管理`页面看到其执行状态。

##### **B4.2. 知识库管理 (Knowledge Base Management)**

* **页面名称**: 知识库管理
* **目的**: 管理项目内用于RAG（检索增强生成）的知识库。
* **导航关系**: 点击左侧导航栏的 **[数据]** -> **[知识库]**。
* **核心UI元素 (UIIE)**:
  * **页面顶栏**: **[+ 新建知识库]** 按钮。
  * **主内容区**: **知识库卡片网格**。每个卡片展示一个知识库。
* **数据实体与交互**:
  * **实体**: `知识库 (Knowledge Base)`
  * **知识库卡片 UI**:
    * **数据展示**: `icon`, `name`, `description`。
    * **交互**: 卡片上有"配置"和"删除"按钮。
  * **新建知识库流程**:
    * 点击 **[+ 新建知识库]** 后，弹出对话框。
    * **表单**: 输入`name`, `description`。选择`source_type` (来源类型)，目前支持`数据集`或`文件`。
    * 如果选择`数据集`，则再提供一个下拉框，用于选择本项目内的一个具体`数据集`。
    * 提交后，系统在后台进行Embedding（向量化）处理，创建一个新的`知识库`。

##### **B4.3. 数据源管理 (Data Source Management)**

* **页面名称**: 数据源管理
* **目的**: 管理项目需要连接的外部数据库信息，供数据治理或应用运行时使用。
* **导航关系**: 点击左侧导航栏的 **[数据]** -> **[数据源]**。
* **核心UI元素 (UIIE)**:
  * **页面顶栏**: **[+ 新建数据源]** 按钮。
  * **主内容区**: **数据源列表表格**。
    * **列**: `name`, `type`(数据库类型), 创建人, 创建时间, 操作。
* **数据实体与交互**:
  * **实体**: `数据源 (Data Source)`
  * **新建/编辑流程**:
    * 点击按钮后弹出完整的页面或模态框。
    * **表单**: `name`, `type` (下拉选择 MySQL, PostgreSQL等), `connection_string` (连接字符串，包含地址、端口、用户名、密码等)。
    * 表单中包含一个 **[测试连接]** 按钮，点击后后端会尝试连接数据库，并返回成功或失败的信息。
    * 只有测试连接成功后，**[保存]** 按钮才可被点击。

#### **B5. 模型 (Model)**

模块用于管理项目空间内的AI模型资产，覆盖从导入、微调到部署服务的全过程。

##### **B5.1. 模型管理 (Model Management)**

* **页面名称**: 模型管理
* **目的**: 管理本项目空间内的所有模型资产，包括从社区导入的基座模型和本项目微调产生的模型。
* **导航关系**: 点击左侧导航栏的 **[模型]** -> **[模型管理]**。
* **核心UI元素 (UIIE)**:
  * **页面顶栏**: **[导入模型]** 按钮。
  * **主内容区**: **模型列表表格**。
    * **列**: `name`(模型名称), `family`(模型系列), `description`, 最新版本号, 创建时间, 操作。
* **数据实体与交互**:
  * **实体**: `模型 (Model)`, `模型版本 (Model Version)`, `任务`
  * **发起微调任务**:
    * 在模型列表的操作列中，有 **[微调]** 按钮。
    * 点击后，弹出对话框创建微调`任务`。
    * **表单**: `name`(任务名), `input_model_version_id`(选择一个基座模型版本), `input_dataset_id`(选择一个训练数据集), `resource_pool_id`(选择一个算力资源池), `output_model_name`(为微调后的新模型命名)。
    * 提交后，创建`任务`记录，可在`B6.1 任务管理`页监控。任务成功后，会在模型管理列表中出现一个新的`模型`。

##### **B5.2. 模型部署管理 (Model Deployment Management)**

* **页面名称**: 模型部署管理
* **目的**: 管理所有已部署的模型服务实例。
* **导航关系**: 点击左侧导航栏的 **[模型]** -> **[模型部署]**。
* **核心UI元素 (UIIE)**:
  * **页面顶栏**: **[+ 新建部署]** 按钮。
  * **主内容区**: **模型部署实例列表表格**。
    * **列**: `name`(部署实例名), `model_version_id`(部署的模型版本), `status`(状态：部署中/运行中/已停止/失败), `replicas`(副本数), `endpoint_url`(服务地址), `resource_pool_id`(所用资源池), 操作。
* **数据实体与交互**:
  * **实体**: `模型部署 (Model Deployment)`, `任务`
  * **新建部署流程**:
    * 点击 **[+ 新建部署]**，弹出对话框创建部署`任务`。
    * **表单**: `name`(部署名), `model_version_id`(选择要部署的模型版本), `resource_pool_id`(选择算力资源池), `replicas`(副本数滑块)。
    * 提交后，创建部署`任务`，此列表页中出现一个"部署中"状态的新条目。任务成功后，状态变为"运行中"，并显示可复制的`endpoint_url`。
  * **操作**: 列表的操作列提供"停止"、"启动"、"删除"等按钮来管理部署实例的生命周期。

#### **B6. 平台工具 (Platform Tools)**

模块提供支撑整个开发流程的底层工具和服务。

##### **B6.1. 任务管理 (Task Management)**

* **页面名称**: 任务管理
* **目的**: 提供一个集中的地方，监控项目空间内所有异步、长时间运行的后台任务（如数据处理、模型微调、模型部署等）。
* **导航关系**: 点击左侧导航栏的 **[平台工具]** -> **[任务管理]**。
* **核心UI元素 (UIIE)**:
  * **主内容区**: **任务列表表格**。
    * **列**: `name`(任务名), `type`(任务类型), `status`(状态:排队中/运行中/成功/失败), 创建人, 创建时间, 耗时, 操作。
    * **筛选器**: 页面顶部提供按 `type` 和 `status` 筛选任务的下拉框。
* **数据实体与交互**:
  * **实体**: `任务 (Task)`
  * **交互**:
    * 列表应支持自动刷新或手动刷新，以更新任务状态。
    * 在操作列中，提供 **[查看日志]** 按钮，点击后弹出模态框，显示该任务的详细输出日志。
    * 对于"运行中"的任务，提供 **[停止]** 按钮。

##### **B6.2. 算子管理 (Operator Management)**

* **页面名称**: 算子管理
* **目的**: 允许开发者查看平台已有的原子操作算子，并能注册自定义的新算子。
* **导航关系**: 点击左侧导航栏的 **[平台工具]** -> **[算子管理]**。
* **核心UI元素 (UIIE)**:
  * **页面顶栏**: **[+ 新建算子]** 按钮。
  * **主内容区**: **算子列表表格**。
    * **列**: `name`(算子名), `type`(算子类型), `endpoint_url`(调用端点), 创建人, 创建时间。
* **数据实体与交互**:
  * **实体**: `算子 (Operator)`
  * **新建算子流程**:
    * 点击 **[+ 新建算子]** 后，弹出对话框。
    * **表单**: `name`, `type`(下拉选择：爬取/清洗/增强等), `endpoint_url`(实现该算子功能的API地址)。
    * 提交后，在`算子`表中创建一条新纪录。

##### **B6.3. 服务管理 (Service Management)**

* **页面名称**: 服务管理
* **目的**: 允许开发者为本项目空间申请访问企业级外部服务（资源端点）的凭证(API Key)。
* **导航关系**: 点击左侧导航栏的 **[平台工具]** -> **[服务管理]**。
* **核心UI元素 (UIIE)**:
  * **页面顶栏**: **[+ 申请密钥]** 按钮。
  * **主内容区**: **API密钥列表表格**。
    * **列**: `resource_endpoint_id`(关联的资源端点名称), `api_key`(部分屏蔽显示的密钥字符串), `status`(有效/失效), 申请人, 申请时间, 操作。
* **数据实体与交互**:
  * **实体**: `API密钥 (API Key)`, `资源端点 (Resource Endpoint)`
  * **申请密钥流程**:
    * 点击 **[+ 申请密钥]**，弹出对话框。
    * **表单**:
      * `resource_endpoint_id`: 下拉列表，展示所有由**企业管理员**注册的`资源端点`。
    * 提交后，后端生成`API密钥`，并显示在列表中。首次显示时应提供 **[复制]** 按钮，并提示用户密钥仅显示一次。
  * **操作**: 列表的操作列提供"失效"按钮，可手动禁用某个密钥。

#### **B7. 项目设置 (Project Settings)**

理项目空间的基本配置、成员和角色。

##### **B7.1. 成员与角色管理 (Member & Role Management)**

* **页面名称**: 项目设置
* **目的**: 管理本项目空间下的成员及其角色分配。
* **导航关系**: 点击左侧导航栏的 **[项目设置]**。
* **核心UI元素 (UIIE)**:
  * **页面顶栏**: **[+ 添加成员]** 按钮。
  * **主内容区**: **成员列表表格**。
    * **列**: `username`(用户名), `role_name`(角色), 加入时间, 操作。
* **数据实体与交互**:
  * **实体**: `用户 (User)`, `角色 (Role)`, `项目空间 (Project Space)` 的关联关系。
  * **添加成员流程**:
    * 点击 **[+ 添加成员]**，弹出对话框。
    * **表单**:
      * **用户选择**: 搜索框，可以从企业的所有用户中搜索并选择一个。
      * **角色分配**: 复选框或下拉列表，选择要分配的角色（开发角色/企业员工等）。
    * 提交后，在成员列表中出现新成员。
  * **操作**: 列表的操作列提供"编辑角色"和"移出项目"功能。

### 6.4 C. 企业管理视图 (Enterprise Admin View)

视图专为`企业管理员`设计，提供对整个平台的全局管控能力。设计目标是**全面、可控、安全**，确保企业资源得到合理分配和有效治理。

#### **C1. 企业概览 (Enterprise Dashboard)**

* **页面名称**: 企业概览
* **目的**: 提供平台级的全局数据看板。
* **核心UI元素**: 类似B2项目概览，但数据维度是全企业。包含：项目空间总数、用户总数、平台总算力资源使用率、已购应用数等。

#### **C2. 组织与项目空间管理 (Orgs & Projects)**

* **页面名称**: 项目空间管理
* **核心UI元素**:
  * **页面顶栏**: **[+ 新建项目空间]** 按钮。
  * **主内容区**: **项目空间列表表格**。
    * **列**: `name`, `description`, 项目管理员, 创建时间, 操作。
* **交互**: 管理员在此处可创建、编辑（修改名称/描述/管理员）、删除项目空间。编辑时可关联`资源池`。

#### **C3. 应用市场管理 (App Market Management)**

* **页面名称**: 企业应用管理
* **核心UI元素**:
  * **页面顶栏**: **[购买增值应用]** 按钮。
  * **主内容区**: 包含两个Tab：**[企业应用列表]** 和 **[应用上升审批]**。
  * **企业应用列表 Tab**:
    * **表格列**: `app_name`, `app_type`, `status`, 操作。
    * **交互**: 点击"分配"按钮，可弹出对话框，将该应用授权给一个或多个项目空间。
  * **应用上升审批 Tab**:
    * **表格列**: 申请的应用名称, 版本, 申请人, 申请时间, 操作。
    * **交互**: 操作列有 **[批准]** 和 **[驳回]** 按钮。批准后，该`项目应用`成为一个`企业应用`。

#### **C4. 算力管理 (Compute Management)**

* **核心UI元素**: 包含两个子页面/Tab: **[算力节点]** 和 **[资源池]**。
* **算力节点页**:
  * **UI**: 算力主机列表，点击可展开看到其下的GPU卡列表。提供 **[+ 登记主机]** 功能。
* **资源池页**:
  * **UI**: 资源池列表（卡片或表格），显示每个池的类型和包含的GPU。提供 **[+ 新建资源池]** 功能，创建后可从空闲GPU中选择加入池内。

#### **C5. 服务治理 (Service Management)**

* **页面名称**: 资源端点管理
* **目的**: 统一注册和管理可供各项目空间申请使用的外部服务。
* **核心UI元素**:
  * **页面顶栏**: **[+ 新建资源端点]** 按钮。
  * **主内容区**: **资源端点列表表格**。
* **交互**: 管理员在此处登记外部服务的URL、名称、描述等信息，创建后即可被开发者在`B6.3`页面看到并申请密钥。

#### **C6. 平台设置 (Platform Settings)**

* **页面名称**: 平台设置
* **目的**: 对平台进行品牌化配置。
* **核心UI元素**: 一个简单的表单，包含`name`(平台名称)输入框和`logo_url`(Logo上传)组件。

## 7. 需求分工

基于6.15版本的基础上，实现以下目标：

1. 上线现有支撑业务的应用，包括知识库、AI+BI、文档评审（国敏、坚坚输出方案）

   1. 应用的运行环境能跟一体机环境整合

   2. 应用能运行多个副本，通过更换配置嵌入到多个项目空间中，包括配置知识库、数据库对接等

2. 增加2C首页，实现2C、2Admin、2Dev的MVP（彦绮、建文）

3. 增加应用集市，实现员工提交应用需求，Admin根据需求购买，员工使用应用的MV
